# Debug de l'erreur 500 lors de l'inscription

## ✅ CORS résolu !
L'erreur CORS est maintenant complètement résolue. Le problème actuel est une erreur 500 du serveur.

## 🔍 Causes possibles de l'erreur 500

### 1. Email déjà utilisé
Même si la validation est commentée dans le code, la base de données a probablement une contrainte unique sur l'email.

**Test** : Essayez avec un email différent, par exemple :
```json
{
  "first_name": "Test",
  "last_name": "User",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "is_professional": false
}
```

### 2. Problème de base de données
- Connexion à la base de données
- Tables manquantes (users, professional_profiles, client_profiles)
- Contraintes de clés étrangères

### 3. Service d'email
L'erreur peut venir de `EmailService::sendVerificationEmail($user)` ligne 146.

### 4. Création des profils
Erreur lors de la création de `ProfessionalProfile` ou `ClientProfile`.

## 🛠️ Solutions de diagnostic

### Option 1 : Activer le mode debug temporairement
Dans le fichier `.env` du serveur de production, ajouter :
```env
APP_DEBUG=true
```

Puis vider le cache :
```bash
php artisan config:cache
```

Cela affichera les détails de l'erreur dans la réponse JSON.

### Option 2 : Consulter les logs Laravel
Sur le serveur de production :
```bash
tail -f storage/logs/laravel.log
```

### Option 3 : Test avec curl et email unique
```bash
curl -X POST https://dev-backend.hi-3d.com/api/register \
  -H "Origin: https://dev2.hi-3d.com" \
  -H "Content-Type: application/json" \
  -H "X-Requested-With: XMLHttpRequest" \
  -H "Accept: application/json" \
  -d '{
    "first_name": "Debug",
    "last_name": "Test", 
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "is_professional": false
  }' \
  -v
```

## 🎯 Prochaines étapes

1. **Tester avec un email unique** pour éliminer le problème d'email dupliqué
2. **Activer APP_DEBUG=true** temporairement pour voir l'erreur exacte
3. **Consulter les logs** Laravel pour identifier la cause
4. **Corriger le problème** identifié
5. **Désactiver APP_DEBUG** en production

## 📝 Note importante

L'erreur CORS est maintenant **complètement résolue** ! Le problème actuel est purement côté serveur Laravel et n'a rien à voir avec CORS.
