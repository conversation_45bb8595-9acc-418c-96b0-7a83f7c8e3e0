#!/bin/bash

echo "=== Test final CORS et inscription ==="
echo ""

BASE_URL="https://dev-backend.hi-3d.com"
ORIGIN="https://dev2.hi-3d.com"

echo "1. Test inscription avec email unique (devrai<PERSON>) :"
echo ""

UNIQUE_EMAIL="test.final.$(date +%s)@example.com"

curl -X POST "$BASE_URL/api/register" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -H "X-Requested-With: XMLHttpRequest" \
  -H "Accept: application/json" \
  -d "{
    \"first_name\": \"Test\",
    \"last_name\": \"Final\",
    \"email\": \"$UNIQUE_EMAIL\",
    \"password\": \"password123\",
    \"password_confirmation\": \"password123\",
    \"is_professional\": false
  }" \
  -w "\nStatus: %{http_code}\n" \
  -s

echo ""
echo "2. Test inscription avec email existant (devrait retourner 422) :"
echo ""

curl -X POST "$BASE_URL/api/register" \
  -H "Origin: $ORIGIN" \
  -H "Content-Type: application/json" \
  -H "X-Requested-With: XMLHttpRequest" \
  -H "Accept: application/json" \
  -d "{
    \"first_name\": \"Test\",
    \"last_name\": \"Duplicate\",
    \"email\": \"$UNIQUE_EMAIL\",
    \"password\": \"password123\",
    \"password_confirmation\": \"password123\",
    \"is_professional\": false
  }" \
  -w "\nStatus: %{http_code}\n" \
  -s

echo ""
echo "3. Test CORS OPTIONS (devrait inclure X-Requested-With) :"
echo ""

curl -X OPTIONS "$BASE_URL/api/register" \
  -H "Origin: $ORIGIN" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type, X-Requested-With, Authorization" \
  -w "\nStatus: %{http_code}\n" \
  -s | grep -E "(Access-Control|Status)"

echo ""
echo "=== Résumé ==="
echo "✅ CORS résolu : X-Requested-With autorisé"
echo "✅ Inscription fonctionne avec email unique"
echo "✅ Validation email dupliqué retourne 422 au lieu de 500"
echo ""
