# Déploiement de la correction CORS

## Problème identifié

Le serveur de production `https://dev-backend.hi-3d.com` retourne :
```
access-control-allow-headers: Origin, Content-Type, Accept, Authorization
```

Il manque `X-Requested-With` dans les en-têtes autorisés !

## Actions à effectuer sur le serveur de production

### 1. Mettre à jour `config/cors.php`

Remplacer la section `allowed_headers` par :

```php
'allowed_headers' => [
    'Accept',
    'Authorization',
    'Content-Type',
    'X-Requested-With',  // ← AJOUTER CETTE LIGNE
    'X-CSRF-TOKEN',
    'X-XSRF-TOKEN',
    'Origin',
    'Cache-Control',
    'Pragma',
],
```

### 2. Mettre à jour `app/Http/Kernel.php`

S'assurer que le middleware CORS est correct :

```php
protected $middleware = [
    \App\Http\Middleware\TrustProxies::class,
    \App\Http\Middleware\HandleCorsOptions::class,  // ← Nouveau middleware
    \Illuminate\Http\Middleware\HandleCors::class,  // ← Middleware Laravel intégré
    \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
    // ... autres middlewares
];
```

### 3. Copier le nouveau middleware

Copier le fichier `app/Http/Middleware/HandleCorsOptions.php` sur le serveur de production.

### 4. Vérifier les variables d'environnement

Dans le fichier `.env` du serveur de production, s'assurer que :

```env
FRONTEND_URL=https://dev2.hi-3d.com
# ou le domaine correct de votre frontend
```

### 5. Vider le cache

Après les modifications, exécuter sur le serveur de production :

```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Test de validation

Après déploiement, tester avec :

```bash
curl -X OPTIONS https://dev-backend.hi-3d.com/api/register \
  -H "Origin: https://dev2.hi-3d.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type, X-Requested-With, Authorization" \
  -v
```

La réponse devrait inclure :
```
access-control-allow-headers: Accept, Authorization, Content-Type, X-Requested-With, X-CSRF-TOKEN, X-XSRF-TOKEN, Origin, Cache-Control, Pragma
```

## Domaine frontend

Vérifiez que votre application frontend utilise bien le domaine `https://dev2.hi-3d.com` ou mettez à jour la configuration CORS avec le bon domaine.
