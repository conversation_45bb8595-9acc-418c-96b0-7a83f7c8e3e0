# Configuration Nginx CORS - Solution définitive

## Problème identifié

Nginx gère les en-têtes CORS au lieu de Laravel. La configuration actuelle :

```
access-control-allow-headers: Origin, Content-Type, Accept, Authorization
```

Il manque `X-Requested-With` !

## Solution : Modifier la configuration Nginx

### 1. Localiser le fichier de configuration

Sur le serveur de production, trouvez le fichier de configuration Nginx pour `dev-backend.hi-3d.com` :

```bash
# Généralement dans :
/etc/nginx/sites-available/dev-backend.hi-3d.com
# ou
/etc/nginx/conf.d/dev-backend.hi-3d.com.conf
```

### 2. Modifier la configuration CORS

Cherchez la section qui ressemble à :

```nginx
# Configuration CORS actuelle (à modifier)
add_header Access-Control-Allow-Origin "https://dev2.hi-3d.com";
add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE";
add_header Access-Control-Allow-Headers "Origin, Content-Type, Accept, Authorization";
add_header Access-Control-Allow-Credentials "true";
```

### 3. Remplacer par la nouvelle configuration

```nginx
# Configuration CORS corrigée
add_header Access-Control-Allow-Origin "https://dev2.hi-3d.com";
add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE";
add_header Access-Control-Allow-Headers "Origin, Content-Type, Accept, Authorization, X-Requested-With, X-CSRF-TOKEN, X-XSRF-TOKEN";
add_header Access-Control-Allow-Credentials "true";

# Gérer les requêtes OPTIONS
if ($request_method = 'OPTIONS') {
    add_header Access-Control-Allow-Origin "https://dev2.hi-3d.com";
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE";
    add_header Access-Control-Allow-Headers "Origin, Content-Type, Accept, Authorization, X-Requested-With, X-CSRF-TOKEN, X-XSRF-TOKEN";
    add_header Access-Control-Allow-Credentials "true";
    add_header Access-Control-Max-Age 86400;
    return 204;
}
```

### 4. Alternative : Déléguer à Laravel

Pour une solution plus flexible, vous pouvez désactiver CORS dans Nginx et laisser Laravel gérer :

```nginx
# Supprimer ou commenter toutes les lignes add_header Access-Control-*
# add_header Access-Control-Allow-Origin "https://dev2.hi-3d.com";
# add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE";
# add_header Access-Control-Allow-Headers "Origin, Content-Type, Accept, Authorization";
# add_header Access-Control-Allow-Credentials "true";
```

### 5. Redémarrer Nginx

Après modification :

```bash
# Tester la configuration
sudo nginx -t

# Redémarrer Nginx
sudo systemctl reload nginx
```

## Test de validation

Après modification, ce test devrait réussir :

```bash
curl -X OPTIONS https://dev-backend.hi-3d.com/api/register \
  -H "Origin: https://dev2.hi-3d.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type, X-Requested-With, Authorization" \
  -v
```

Résultat attendu :
```
access-control-allow-headers: Origin, Content-Type, Accept, Authorization, X-Requested-With, X-CSRF-TOKEN, X-XSRF-TOKEN
```

## Domaine frontend

Vérifiez que votre application frontend utilise bien `https://dev2.hi-3d.com` ou mettez à jour la configuration Nginx avec le bon domaine.
