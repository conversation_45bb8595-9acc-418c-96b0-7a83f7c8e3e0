# Solution temporaire côté frontend

## Problème
Le serveur de production n'autorise pas l'en-tête `X-Requested-With`.

## Solution temporaire

Dans votre fichier `api.ts`, modifiez la configuration des requêtes pour ne pas inclure `X-Requested-With` :

### Avant (qui cause l'erreur) :
```typescript
const response = await fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',  // ← RETIRER TEMPORAIREMENT
    'Accept': 'application/json'
  },
  body: JSON.stringify(data),
  credentials: 'include'
});
```

### Après (solution temporaire) :
```typescript
const response = await fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
    // X-Requested-With retiré temporairement
  },
  body: JSON.stringify(data),
  credentials: 'include'
});
```

## Important

Cette solution est **temporaire**. L'en-tête `X-Requested-With` est important pour la sécurité et devrait être restauré une fois que le serveur de production est mis à jour.

## Solution définitive

Déployez les corrections CORS sur le serveur de production comme décrit dans `deploy_cors_fix.md`.
